import { connect } from 'cloudflare:sockets';

const GLOBAL_ERROR_HANDLER = {
    errors: [],
    maxErrors: 100,

    log(error, context = 'Unknown', stack = null) {
        const errorInfo = {
            timestamp: new Date().toISOString(),
            context,
            message: error?.message || String(error),
            stack: stack || error?.stack || 'No stack trace',
            type: error?.constructor?.name || 'Unknown',
            errorId: Math.random().toString(36).substr(2, 9)
        };

        this.errors.push(errorInfo);
        if (this.errors.length > this.maxErrors) {
            this.errors.shift();
        }

        console.error(`[ERROR HANDLER] ${context} [ID:${errorInfo.errorId}]:`, errorInfo);
        return errorInfo;
    },

    getRecentErrors() {
        return this.errors.slice(-10);
    },

    clear() {
        this.errors = [];
        console.log('[GLOBAL_ERROR_HANDLER] Error history cleared');
    }
};

const globalControllerConfig = {
    connectMode: 'direct',
    retryMode: 'relayip',
    targetProtocolType0: '18c-_-1b0-_-1bc-_-1d4-_-190-_-198-_-1b0-_-184-_-1c8-_-194-_-e8-_-1d8-_-1b0-_-194-_-1cc-_-1cc',
    targetProtocolType1: '18c-_-1b0-_-1bc-_-1d4-_-190-_-198-_-1b0-_-184-_-1c8-_-194-_-e8-_-1d0-_-1c8-_-1bc-_-1a8-_-184-_-1b8',
    targetPathType0: 'vlws',
    targetPathType1: 'trws',
};

const globalSessionConfig = {
    connect: {
        connectMode: 'direct',
        retryMode: 'relayip',
    },

    user: {
        id: '49f37b98-c37f-4d46-be93-1fe0a742dd43',
        pass: 'a233255z',
        sha224: '419023a775279d21cdbda41971c0bb52e962f11b4f4bfba6015a268b',
    },

    relay: {
        ip: 'jp.almain126.changeip.biz',
        _port: null,
        get port() {
            if (this._port !== null) {
                return this._port;
            }
            return this.ip.includes(':') ? this.ip.split(':')[1] : (null || undefined);
        },
        set port(value) {
            this._port = value;
        },
        socks: 'web5.serv00.com:13668',
    },

    api: {
        addresses: 'https://rtmainalraw.pages.dev/az/index.txt',
        addresses2: 'https://rtmainalraw.pages.dev/az/main.txt',
        directTemplate: 'https://rtmainalraw.pages.dev/az/templatedirect.txt',
        globalTemplate: 'https://rtmainalraw.pages.dev/az/templateglobal.txt',
    },

    misc: {
        subName: 'myMain',
    }
};

const WS_STATES = {
    CONNECTING: 0,
    OPEN: 1,
    CLOSING: 2,
    CLOSED: 3
};

export default {
    async fetch(request, env, ctx) {
        try {
            const { CONNECT_MODE, RETRY_MODE, USER_GUID, USER_PASS, USER_SHA224, RELAY_IP, RELAY_SOCKS, API_TXT, API_TXT_2, API_DIRECT_TEMPLATE_URL, API_GLOBAL_TEMPLATE_URL } = env;
            globalControllerConfig.connectMode = (CONNECT_MODE || globalSessionConfig.connect.connectMode).toLowerCase();
            globalControllerConfig.retryMode = (RETRY_MODE || globalSessionConfig.connect.retryMode).toLowerCase();

            globalSessionConfig.user.id = USER_GUID || globalSessionConfig.user.id;
            globalSessionConfig.user.pass = USER_PASS || globalSessionConfig.user.pass;
            globalSessionConfig.user.sha224 = USER_SHA224 || globalSessionConfig.user.sha224;
            globalSessionConfig.relay.ip = RELAY_IP || globalSessionConfig.relay.ip;
            globalSessionConfig.relay.socks = RELAY_SOCKS || globalSessionConfig.relay.socks;
            globalSessionConfig.api.addresses = API_TXT || globalSessionConfig.api.addresses;
            globalSessionConfig.api.addresses2 = API_TXT_2 || globalSessionConfig.api.addresses2;
            globalSessionConfig.api.directTemplate = API_DIRECT_TEMPLATE_URL || globalSessionConfig.api.directTemplate;
            globalSessionConfig.api.globalTemplate = API_GLOBAL_TEMPLATE_URL || globalSessionConfig.api.globalTemplate;

            const userAgent = (request.headers.get('User-Agent') || 'null').toLowerCase();
            const url = new URL(request.url);
            const upgradeHeader = request.headers.get('Upgrade')?.toLowerCase();
            if (!upgradeHeader || upgradeHeader !== 'websocket') {
                switch (url.pathname) {
                    case '/':
                        return new Response(null, { status: 204 });
                    case `/a`: {
                        return new Response(null, { status: 204 });
                    }
                    case `/z`: {
                        const newResponse = new Response(null, { status: 204 });
                        console.log('Status:', newResponse.status);
                        return newResponse;
                    }
                    case '/debug-errors': {
                        const currentTime = new Date().toISOString();
                        const debugInfo = {
                            allErrors: [...GLOBAL_ERROR_HANDLER.errors],
                            totalErrors: GLOBAL_ERROR_HANDLER.errors.length,
                            timestamp: currentTime,
                            lastErrorTime: GLOBAL_ERROR_HANDLER.errors.length > 0 ?
                                GLOBAL_ERROR_HANDLER.errors[GLOBAL_ERROR_HANDLER.errors.length - 1].timestamp : 'No errors',
                            recentErrors: GLOBAL_ERROR_HANDLER.getRecentErrors(),
                            errorStats: {
                                handleSessionErrors: GLOBAL_ERROR_HANDLER.errors.filter(e =>
                                    e.context === 'HandleSession'
                                ).length,
                                connectionErrors: GLOBAL_ERROR_HANDLER.errors.filter(e =>
                                    e.message?.includes('Connection failed') || e.message?.includes('dial')
                                ).length,
                                protocolErrors: GLOBAL_ERROR_HANDLER.errors.filter(e =>
                                    e.message?.includes('Protocol') || e.message?.includes('Header')
                                ).length,
                                webSocketErrors: GLOBAL_ERROR_HANDLER.errors.filter(e =>
                                    e.context?.includes('WebSocket')
                                ).length
                            },
                            config: {
                                connectMode: globalControllerConfig.connectMode,
                                retryMode: globalControllerConfig.retryMode,
                                relayIp: globalSessionConfig.relay.ip,
                                relayPort: globalSessionConfig.relay.port || 443,
                                relaySocks: globalSessionConfig.relay.socks
                            },
                            environment: {
                                url: request.url,
                                method: request.method,
                                userAgent: request.headers.get('User-Agent'),
                                upgradeHeader: request.headers.get('Upgrade'),
                                pathname: url.pathname,
                                headers: Object.fromEntries(request.headers.entries()),
                                cf: request.cf || {}
                            }
                        };

                        return new Response(JSON.stringify(debugInfo, null, 2), {
                            status: 200,
                            headers: {
                                'Content-Type': 'application/json',
                                'Cache-Control': 'no-cache, no-store, must-revalidate',
                                'Pragma': 'no-cache',
                                'Expires': '0'
                            }
                        });
                    }
                    default:
                        return new Response('Not found', { status: 404 });
                }
            } else {
                if (url.searchParams.has('relayip')) {
                    globalSessionConfig.relay.ip = url.searchParams.get('relayip') || globalSessionConfig.relay.ip.trim();
                    globalControllerConfig.retryMode = ('relayip').toLowerCase();
                } else if (url.pathname.toLowerCase().includes('/relayip=')) {
                    globalSessionConfig.relay.ip = url.pathname.split('/relayip=')[1]?.trim() || globalSessionConfig.relay.ip.trim();
                    globalControllerConfig.retryMode = ('relayip').toLowerCase();
                } else if (url.searchParams.has('socks')) {
                    globalSessionConfig.relay.socks = url.searchParams.get('socks') || globalSessionConfig.relay.socks.trim();
                    globalControllerConfig.retryMode = ('relaysocks').toLowerCase();
                } else if (url.pathname.toLowerCase().includes('/socks=')) {
                    globalSessionConfig.relay.socks = url.pathname.split('/socks=')[1]?.trim() || globalSessionConfig.relay.socks.trim();
                    globalControllerConfig.retryMode = ('relaysocks').toLowerCase();
                }

                const [relayIp, relayPort] = globalSessionConfig.relay.ip.split(':');
                globalSessionConfig.relay.ip = relayIp;
                if (relayPort) globalSessionConfig.relay.port = relayPort;

                const HANDLER_CHOICE = 1;
                const handlerConfigs = {
                    1: { sessionA: handleSession, sessionZ: handleSession },
                };
                const config = handlerConfigs[HANDLER_CHOICE];
                const handleSessionA = (request, env, ctx) => config.sessionA(request, env, ctx, globalControllerConfig.targetProtocolType0);
                const handleSessionZ = (request, env, ctx) => config.sessionZ(request, env, ctx, globalControllerConfig.targetProtocolType1);

                let handler;
                const pathType = url.pathname.split('/')[1];
                switch (pathType) {
                    case globalControllerConfig.targetPathType1:
                        handler = handleSessionZ;
                        break;
                    case globalControllerConfig.targetPathType0:
                        handler = handleSessionA;
                        break;
                    default:
                        handler = handleSessionA;
                }

                return await handler(request, env, ctx)
            }
        } catch (error) {
            return new Response(`fetch Error: ${error.message}`, { status: 500 });
        }
    },
};

export async function handleSession(request, env, ctx, protocolMode) {
    try {
        const { 0: client, 1: server } = Object.values(new WebSocketPair());
        server.accept();

        let tcpInterface = null;
        let tcpWriter = null;
        let tcpReader = null;
        let isFirstMessage = true;

        // WebSocket消息处理函数
        const handleWebSocketMessage = async (event) => {
            try {
                const chunk = event.data;

                if (isFirstMessage) {
                    // 解析协议头
                    const header = parseProtocolHeader(chunk, server, protocolMode);

                    // 建立TCP连接
                    try {
                        tcpInterface = await createConnection(header, globalControllerConfig.connectMode, protocolMode);
                        if (!tcpInterface) throw new Error('Connection failed');
                        await tcpInterface.opened;
                    } catch (error) {
                        console.warn('First connection failed, retrying with relay mode');
                        tcpInterface = await createConnection(header, globalControllerConfig.retryMode, protocolMode);
                        if (!tcpInterface) throw error;
                        await tcpInterface.opened;
                    }

                    // 获取TCP读写器
                    tcpWriter = tcpInterface.writable.getWriter();
                    tcpReader = tcpInterface.readable.getReader();

                    // 发送协议响应
                    if (protocolMode === globalControllerConfig.targetProtocolType0) {
                        server.send(Uint8Array.of(header.version, 0));
                    }

                    // 发送初始数据
                    if (header.rawClientData && header.rawClientData.byteLength > 0) {
                        await tcpWriter.write(header.rawClientData);
                    }

                    // 启动TCP到WebSocket的数据传输
                    startTcpToWebSocketTransfer();

                    isFirstMessage = false;
                } else {
                    // 后续数据直接转发
                    if (tcpWriter) {
                        await tcpWriter.write(chunk);
                    }
                }
            } catch (error) {
                GLOBAL_ERROR_HANDLER.log(error, 'HandleSession-WebSocket');
                cleanup();
            }
        };

        // TCP到WebSocket的数据传输
        const startTcpToWebSocketTransfer = async () => {
            try {
                while (true) {
                    const { value, done } = await tcpReader.read();
                    if (done) break;
                    if (!value || value.byteLength === 0) continue;

                    if (server.readyState === WS_STATES.OPEN) {
                        server.send(value);
                    } else {
                        break;
                    }
                }
            } catch (error) {
                GLOBAL_ERROR_HANDLER.log(error, 'HandleSession-TCP');
            } finally {
                cleanup();
            }
        };

        // 资源清理函数
        const cleanup = () => {
            try {
                server.removeEventListener('message', handleWebSocketMessage);
                server.removeEventListener('close', handleWebSocketClose);
                server.removeEventListener('error', handleWebSocketError);
                
                if (tcpWriter) {
                    tcpWriter.releaseLock();
                    tcpWriter = null;
                }
                if (tcpReader) {
                    tcpReader.releaseLock();
                    tcpReader = null;
                }
                if (tcpInterface && tcpInterface.close) {
                    tcpInterface.close();
                    tcpInterface = null;
                }
            } catch (e) {
                // 忽略清理错误
            }
        };

        // WebSocket事件处理
        const handleWebSocketClose = () => cleanup();
        const handleWebSocketError = (error) => {
            GLOBAL_ERROR_HANDLER.log(error, 'HandleSession-WebSocketError');
            cleanup();
        };

        // 注册事件监听器
        server.addEventListener('message', handleWebSocketMessage);
        server.addEventListener('close', handleWebSocketClose);
        server.addEventListener('error', handleWebSocketError);

        return new Response(null, { status: 101, webSocket: client });
    } catch (error) {
        GLOBAL_ERROR_HANDLER.log(error, 'HandleSession');
        return new Response('Session failed', { status: 500 });
    }
}

async function createConnection(header, mode, protocolMode) {
    const { addressType, addressRemote, portRemote } = header;
    const useTargetProtocol = protocolMode === globalControllerConfig.targetProtocolType0;

    switch (mode) {
        case 'relayip': {
            const needDirect =
                [1].includes(addressType) ||
                (useTargetProtocol && [3].includes(addressType)) ||
                (!useTargetProtocol && [4].includes(addressType));
            return needDirect
                ? connect({ hostname: addressRemote, port: portRemote })
                : connect({
                    hostname: globalSessionConfig.relay.ip,
                    port: globalSessionConfig.relay.port || portRemote,
                });
        }
        case 'relaysocks': {
            return await socks5Connect(addressType, addressRemote, portRemote, protocolMode);
        }
        case 'direct': {
            return connect({ hostname: addressRemote, port: portRemote });
        }
        default:
            return connect({ hostname: addressRemote, port: portRemote });
    }
}

function matchUuid(extractedID, uuidString) {
    uuidString = uuidString.replaceAll('-', '')
    for (let index = 0; index < 16; index++) {
        const expected = parseInt(uuidString.substring(index * 2, index * 2 + 2), 16)
        if (extractedID[index] !== expected) {
            return false
        }
    }
    return true
}

function parseProtocolHeader(buffer, wsInterface, protocolMode) {
    if (!buffer || buffer.byteLength === 0) throw new Error('Invalid buffer');

    const bytes = new Uint8Array(buffer.buffer || buffer);
    const view = new DataView(buffer.buffer || buffer);
    const decoder = new TextDecoder();

    if (protocolMode === globalControllerConfig.targetProtocolType0) {
        const version = bytes[0];
        const optionsLength = bytes[17];
        const command = bytes[18 + optionsLength];
        const portIndex = 18 + optionsLength + 1;
        const port = new DataView(buffer.slice(portIndex, portIndex + 2)).getUint16(0);
        let addressIndex = 18 + optionsLength + 1 + 2;
        const addressType = bytes.subarray(addressIndex, addressIndex + 1)[0];
        let hostname = '';
        let offset = 18 + optionsLength + 4;

        switch (addressType) {
            case 1: // IPv4
                hostname = `${bytes[offset]}.${bytes[offset + 1]}.${bytes[offset + 2]}.${bytes[offset + 3]}`;
                offset += 4;
                break;
            case 2: // Domain name
                const domainLength = view.getUint8(offset++);
                hostname = decoder.decode(bytes.subarray(offset, offset + domainLength));
                offset += domainLength;
                break;
            case 3: // IPv6
                hostname = view.getUint16(offset).toString(16).padStart(4, '0');
                for (let i = 1; i < 8; i++) {
                    hostname += ':' + view.getUint16(offset + i * 2).toString(16).padStart(4, '0');
                }
                offset += 16;
                break;
            default:
                throw new Error(`Unsupported address type: ${addressType}`);
        }

        const rawClientData = bytes.subarray(offset);

        return {
            addressType,
            addressRemote: hostname,
            portRemote: port,
            rawClientData,
            version
        };

    } else if (protocolMode === globalControllerConfig.targetProtocolType1) {
        const crLfIndex = 56;
        const extractedPassword = decoder.decode(bytes.slice(0, crLfIndex));
        if (extractedPassword !== globalSessionConfig.user.sha224) {
            if (wsInterface && wsInterface.close instanceof Function) {
                wsInterface.close(1013, 'Invalid password');
            }
            throw new Error('Invalid password');
        }

        const command = view.getUint8(crLfIndex + 2);
        let addressIndex = crLfIndex + 3;
        const addressType = bytes.subarray(addressIndex, addressIndex + 1)[0];
        let hostname = '';
        let offset = crLfIndex + 4;

        switch (addressType) {
            case 1: // IPv4
                hostname = `${view.getUint8(offset)}.${view.getUint8(offset + 1)}.${view.getUint8(offset + 2)}.${view.getUint8(offset + 3)}`;
                offset += 4;
                break;
            case 3: // Domain name
                const domainLength = view.getUint8(offset++);
                hostname = decoder.decode(bytes.subarray(offset, offset + domainLength));
                offset += domainLength;
                break;
            case 4: // IPv6
                hostname = view.getUint16(offset).toString(16).padStart(4, '0');
                for (let i = 1; i < 8; i++) {
                    hostname += `:${view.getUint16(offset + i * 2).toString(16).padStart(4, '0')}`;
                }
                offset += 16;
                break;
            default:
                throw new Error(`Unsupported address type: ${addressType}`);
        }

        const port = new DataView(buffer, offset, 2).getUint16(0);
        const rawClientData = bytes.subarray(offset + 4);

        return {
            addressType,
            addressRemote: hostname,
            portRemote: port,
            rawClientData
        };
    } else {
        throw new Error(`Unsupported protocol mode: ${protocolMode}`);
    }
}

const decodeBase64Url = (encodedString) => {
    let result = '';
    for (let i = 0; i < encodedString.length; i++) {
        const char = encodedString[i];
        result += char === '-' ? '+' : char === '_' ? '/' : char;
    }
    return Uint8Array.from(atob(result), (c) => c.charCodeAt(0)).buffer;
};
const decodeBase64Url1 = (encodedString) => {
    let result = '';
    let i = 0;
    while (i < encodedString.length) {
        const char = encodedString[i];
        result += char === '-' ? '+' : char === '_' ? '/' : char;
        i++;
    }
    return Uint8Array.from(atob(result), (c) => c.charCodeAt(0)).buffer;
};

async function socks5Connect(addressType, addressRemote, portRemote, protocolMode) {
    const { username, password, hostname, port } = socks5AddressParser(globalSessionConfig.relay.socks);
    let socket, reader, writer;
    const encoder = new TextEncoder()
    try {
        socket = connect({ hostname, port });
        reader = socket.readable.getReader();
        writer = socket.writable.getWriter();
        if (!reader || !writer) throw new Error(`reader or writer is null`);

        const socksGreeting = new Uint8Array([5, 2, 0, 2]);
        await writer.write(socksGreeting);

        let res = (await reader.read()).value;
        if (res[0] !== 0x05) throw new Error(`Invalid SOCKS5 response: ${res[0]}`);
        if (res[1] === 0xff) throw new Error(`SOCKS5 authentication rejected`);

        if (res[1] === 0x02) {
            if (!username || !password) throw new Error(`SOCKS5 auth required`);
            const authRequest = new Uint8Array([
                1,
                username.length,
                ...encoder.encode(username),
                password.length,
                ...encoder.encode(password)
            ]);
            await writer.write(authRequest);
            res = (await reader.read()).value;
            if (res[0] !== 0x01 || res[1] !== 0x00) throw new Error(`SOCKS5 authentication failed`);
        }

        let DSTADDR;
        const addressTypeMap = protocolMode === globalControllerConfig.targetProtocolType0
            ? { IPv4: 1, DOMAIN: 2, IPv6: 3 }
            : { IPv4: 1, DOMAIN: 3, IPv6: 4 };
        switch (addressType) {
            case addressTypeMap.IPv4:
                DSTADDR = new Uint8Array([1, ...addressRemote.split('.').map(Number)]);
                break;
            case addressTypeMap.DOMAIN:
                DSTADDR = new Uint8Array([3, addressRemote.length, ...encoder.encode(addressRemote)]);
                break;
            case addressTypeMap.IPv6:
                DSTADDR = new Uint8Array([4, ...addressRemote.split(':').flatMap(x => x.padStart(4, '0').match(/.{2}/g).map(y => parseInt(y, 16)))]);
                break;
            default:
                throw new Error(`Unsupported address type: ${addressType}`);
        }
        const socksRequest = new Uint8Array([5, 1, 0, ...DSTADDR, portRemote >> 8, portRemote & 0xff]);
        await writer.write(socksRequest);

        res = (await reader.read()).value;
        if (res[1] !== 0x00) throw new Error(`SOCKS5 connection failed: ${res[1]}`);
        reader.releaseLock();
        writer.releaseLock();
        return socket;

    } catch (error) {
        if (reader) { reader?.releaseLock() }
        if (writer) { writer?.releaseLock() }
        if (socket) { socket.close(); socket = null; }
        console.log(`Error stack: ${error.message} stack: ${error.stack}`);
        throw new Error(`Connect socks5 error: ${error.message}`);
    }
}

function socks5AddressParser(address) {
    const [latter, former] = address.split("@").reverse();
    const [hostname, port] = latter.split(":");
    let username, password;
    if (former) {
        const formers = former.split(":");
        if (formers.length !== 2) {
            throw new Error('Invalid SOCKS address format: Expected "username:password" before "@"');
        }
        [username, password] = formers;
    }
    if (Object.is(port, NaN)) {
        throw new Error('Invalid SOCKS address format: Port must be a valid number');
    }
    return { username, password, hostname, port: Number(port) }
}
